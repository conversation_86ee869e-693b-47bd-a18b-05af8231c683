/* Detail Page Styles */
.detail-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding-top: 0;
  /* Remove top padding since we have TopNavigationBar */
}

/* Main Content */
.detail-main {
  flex: 1;
  padding: 0;
  background-color: #ffffff;
}

/* Detail Header - positioned below TopNavigationBar */
.detail-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 50;
}

.back-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.back-btn:hover {
  background-color: #f5f5f5;
}

/* Product Detail Container */
.product-detail-container {
  display: flex;
  gap: 40px;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Product Images Section */
.product-images-section {
  flex: 1;
  max-width: 500px;
}

.main-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f8f9fa;
  margin-bottom: 16px;
}

.product-main-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.variant-label {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

/* Thumbnails */
.thumbnail-images {
  margin-top: 16px;
}

.thumbnail-container {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
  scroll-behavior: smooth;
}

.thumbnail-container::-webkit-scrollbar {
  height: 6px;
}

.thumbnail-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.thumbnail-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.thumbnail-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.thumbnail {
  min-width: 80px;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.thumbnail.active {
  border-color: #E6B120;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Product Info Section */
.product-info-section {
  flex: 1;
  max-width: 600px;
}

.product-header {
  margin-bottom: 16px;
}

.title-rating {
  width: 100%;
}

.product-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.rating-text {
  color: #666;
  font-weight: 500;
}

.rating-count {
  color: #999;
  font-size: 13px;
}

.stars {
  display: flex;
  gap: 2px;
  color: #E6B120;
}

.rating-text {
  font-family: 'Istok Web', sans-serif;
  font-weight: 400;
  color: #838383;
  padding: 0 8px 0 0;
  border-right: 1px solid #838383;
}

.rating-count {
  font-family: 'Istok Web', sans-serif;
  color: #838383;
  font-size: 13px;
}

.sold-text {
  color: #6c757d;
  font-weight: 500;
}

.price-section {
  border-bottom: 1px solid #e7e7e7;
  padding: 0 0 16px 0;
  margin-bottom: 16px;
}

.price-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  flex: 1;
}

.wishlist-btn {
  background: none;
  border: 2px solid #E6B120;
  color: #E6B120;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 18px;
  width: 48px;
  height: 48px;
  min-width: 48px;
  flex-shrink: 0;
}

.wishlist-btn:hover {
  background-color: #E6B120;
  color: white;
  transform: scale(1.05);
}

.wishlist-btn.added {
  background-color: #E6B120;
  border-color: #E6B120;
  color: white;
  animation: heartBeat 0.6s ease-in-out;
}

.wishlist-btn.added:hover {
  background-color: #d4a017;
  border-color: #d4a017;
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

/* Variants */
.variants-section {
  margin: 1rem 0 0 0;
  padding: 0 0 2rem 0;
  border-bottom: 1px solid #e7e7e7;
}

.variants-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.variants-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.variant-box {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
}


/* Description */
.description-section {
  margin-bottom: 24px;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 12px 0 12px 0;
}

.brand-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.category-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.description-text {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  white-space: pre-line;
  /* Preserve line breaks */
}

/* Related Products */
.related-products-section {
  padding: 32px 20px 40px 20px;
  border-top: 1px solid #e7e7e7;
  background-color: #fafafa;
  margin-top: 20px;
}

.related-products-container {
  max-width: 1200px;
  margin: 0 auto;
}

.related-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: left;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  justify-items: center;
}

.related-card {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  max-width: 220px;
  height: fit-content;
}

.related-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.related-image {
  width: 100%;
  aspect-ratio: 1;
  background-color: #f8f9fa;
  overflow: hidden;
  position: relative;
}

.related-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.related-card:hover .related-image img {
  transform: scale(1.05);
}

.related-info {
  padding: 14px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.related-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.8em;
}

.related-price {
  font-size: 16px;
  font-weight: 600;
  color: #E6B120;
  margin: 4px 0;
}

.related-rating {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 12px;
  margin-top: auto;
}

.related-stars {
  color: #FFD700;
  font-size: 12px;
  letter-spacing: 1px;
}

.related-sold {
  color: #666;
  font-size: 11px;
  white-space: nowrap;
}

/* Responsive Design */
@media (min-width: 768px) {
  .product-container {
    flex-direction: row;
    gap: 32px;
  }

  .product-images {
    flex: 1;
    max-width: 400px;
  }

  .product-info {
    flex: 1;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
  }

  .related-products-section {
    padding: 40px 24px 48px 24px;
  }
}

@media (min-width: 1024px) {
  .detail-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 28px;
  }

  .product-title {
    font-size: 28px;
  }

  .price {
    font-size: 32px;
  }

  .related-products-section {
    padding: 48px 32px 56px 32px;
  }
}

@media (min-width: 1440px) {
  .detail-main {
    max-width: 1400px;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 32px;
  }

  .related-products-section {
    padding: 56px 40px 64px 40px;
  }
}

/* Desktop specific styles */
@media (min-width: 1024px) {
  .bottom-nav {
    display: none;
    /* Hide bottom nav on desktop */
  }
}

/* Tablet specific styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
  }
}

/* Mobile specific styles */
@media (max-width: 767px) {
  .detail-main {
    padding: 12px;
  }

  .product-title {
    font-size: 20px;
  }

  .price {
    font-size: 24px;
  }

  .related-products-section {
    padding: 24px 16px 32px 16px;
    margin-top: 16px;
  }

  .related-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .related-card {
    max-width: none;
  }

  .related-info {
    padding: 12px;
  }

  .related-name {
    font-size: 13px;
    min-height: 2.6em;
  }

  .related-price {
    font-size: 14px;
  }

  .related-rating {
    font-size: 11px;
  }

  .related-sold {
    font-size: 10px;
  }
}

/* Extra small mobile */
@media (max-width: 480px) {
  .related-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .related-products-section {
    padding: 20px 12px 28px 12px;
  }

  .related-info {
    padding: 10px;
  }

  .related-name {
    font-size: 12px;
  }

  .related-price {
    font-size: 13px;
  }
}

/* ===== ACTION BUTTONS ===== */

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.add-to-cart-btn,
.buy-now-btn {
  flex: 1;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 56px;
}

.add-to-cart-btn {
  background-color: #f8f9fa;
  color: #E6B120;
  border: 2px solid #E6B120;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #E6B120;
  color: white;
}

.buy-now-btn {
  background-color: #E6B120;
  color: white;
}

.buy-now-btn:hover:not(:disabled) {
  background-color: #d4a017;
}

.add-to-cart-btn:disabled,
.buy-now-btn:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  border-color: #e9ecef;
  cursor: not-allowed;
}

/* ===== STOCK INFO ===== */

.stock-info {
  margin: 16px 0;
}

.stock-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.stock-indicator.in-stock {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.stock-indicator.out-of-stock {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.stock-indicator i {
  font-size: 16px;
}

/* ===== TOAST NOTIFICATIONS ===== */

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast-notification {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== LOADING STATES ===== */

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #E6B120;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.no-data-text,
.error-text {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
  grid-column: span 2;
}

.error-container {
  text-align: center;
  padding: 40px 20px;
}

.error-container h2 {
  color: #e74c3c;
  margin-bottom: 16px;
}

.error-container p {
  color: #666;
  margin-bottom: 24px;
}

.back-btn,
.retry-btn {
  padding: 10px 20px;
  margin: 0 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover,
.retry-btn:hover {
  background-color: #f8f9fa;
  border-color: #E6B120;
}

/* ===== SKELETON LOADING ===== */

.product-skeleton {
  display: flex;
  flex-direction: column;
  gap: 24px;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.product-images-skeleton {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-image-skeleton {
  width: 100%;
  aspect-ratio: 1;
  background-color: #f0f0f0;
  border-radius: 12px;
}

.thumbnails-skeleton {
  display: flex;
  gap: 8px;
}

.thumbnail-skeleton {
  width: 60px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.product-info-skeleton {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.title-skeleton {
  height: 28px;
  background-color: #f0f0f0;
  border-radius: 4px;
  width: 80%;
}

.rating-skeleton {
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 4px;
  width: 60%;
}

.price-skeleton {
  height: 32px;
  background-color: #f0f0f0;
  border-radius: 4px;
  width: 40%;
}

.description-skeleton {
  height: 80px;
  background-color: #f0f0f0;
  border-radius: 4px;
  width: 100%;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 767px) {
  .product-detail-container {
    flex-direction: column;
    gap: 20px;
    padding: 16px;
    margin-top: 10px;
  }

  .product-header {
    margin-bottom: 12px;
  }

  .product-title {
    font-size: 18px;
    line-height: 1.3;
  }

  .product-rating {
    font-size: 13px;
  }

  .price-section {
    padding: 0 0 12px 0;
    margin-bottom: 12px;
  }

  .price-container {
    gap: 12px;
  }

  .price {
    font-size: 22px;
  }

  .wishlist-btn {
    width: 40px;
    height: 40px;
    min-width: 40px;
    font-size: 16px;
  }

  .wishlist-btn i {
    font-size: 16px;
  }
}